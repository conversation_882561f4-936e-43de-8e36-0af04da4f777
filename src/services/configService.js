const fs = require("node:fs");
const path = require("node:path");
const logger = require("../utils/logger");

class ConfigService {
  constructor() {
    this.configPath = path.join(__dirname, "..", "..", "runtime_config.json");
    this.config = this.loadConfig();
    this.listeners = [];
  }

  loadConfig() {
    try {
      if (fs.existsSync(this.configPath)) {
        const data = fs.readFileSync(this.configPath, "utf8");
        return JSON.parse(data);
      }
    } catch (error) {
      logger.warn("Error loading runtime config, using defaults:", error);
    }

    // Default configuration
    const defaultPromptTemplate = "turing4";
    // Use Peter as default for turing4, fallback to env or Nonce
    const defaultBotName = process.env.BOT_NAME || "Peter";

    return {
      provider: process.env.MODEL_PROVIDER || "gemini",
      model:
        process.env.MODEL ||
        this.getDefaultModel(process.env.MODEL_PROVIDER || "gemini"),
      botName: defaultBotName,
      rateLimit: Number.parseInt(process.env.RATE_LIMIT || "5"),
      rateLimitWindow: Number.parseInt(
        process.env.RATE_LIMIT_WINDOW || "60000"
      ),
      maxContextMessages: Number.parseInt(
        process.env.MAX_CONTEXT_MESSAGES || "10"
      ),
      groupOnly: process.env.GROUP_ONLY === "true",
      promptTemplate: defaultPromptTemplate, // Default to getTuringPrompt4
      customPrompt: "", // Custom prompt text for when promptTemplate is "custom"
      temperature: 1.2,
    };
  }

  getDefaultModel(provider) {
    switch (provider) {
      case "groq":
        return "llama-3.3-70b-versatile";
      case "gemini":
        return "gemini-2.0-flash";
      case "openai":
        return "gpt-4o";
      default:
        return "gemini-2.0-flash";
    }
  }

  saveConfig() {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
      logger.info("Runtime configuration saved");
    } catch (error) {
      logger.error("Error saving runtime config:", error);
    }
  }

  get(key) {
    return this.config[key];
  }

  set(key, value) {
    const oldValue = this.config[key];
    this.config[key] = value;
    this.saveConfig();

    // Notify listeners of the change
    this.notifyListeners(key, value, oldValue);

    logger.info(`Configuration updated: ${key} = ${value}`);
  }

  getAll() {
    return { ...this.config };
  }

  addListener(callback) {
    this.listeners.push(callback);
  }

  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Get the default bot name for a specific prompt template
   * @param {string} promptId - The prompt template ID
   * @returns {string|null} - The default bot name or null if no default
   */
  getDefaultBotNameForPrompt(promptId) {
    const prompts = this.getAvailablePrompts();
    const prompt = prompts.find((p) => p.id === promptId);
    return prompt ? prompt.defaultBotName : null;
  }

  /**
   * Update configuration with automatic bot name setting for prompt changes
   * @param {Object} updates - Configuration updates
   * @returns {Object} - Changes made
   */
  updateMultiple(updates) {
    const changes = {};

    // If promptTemplate is being updated, automatically set the bot name
    if (
      updates.promptTemplate &&
      updates.promptTemplate !== this.config.promptTemplate
    ) {
      const defaultBotName = this.getDefaultBotNameForPrompt(
        updates.promptTemplate
      );
      if (defaultBotName) {
        updates.botName = defaultBotName;
        logger.info(
          `Auto-setting bot name to "${defaultBotName}" for prompt "${updates.promptTemplate}"`
        );
      }
    }

    for (const [key, value] of Object.entries(updates)) {
      if (this.config.hasOwnProperty(key)) {
        const oldValue = this.config[key];
        this.config[key] = value;
        changes[key] = { oldValue, newValue: value };
      }
    }

    this.saveConfig();

    // Notify listeners of all changes
    for (const [key, change] of Object.entries(changes)) {
      this.notifyListeners(key, change.newValue, change.oldValue);
    }

    logger.info("Multiple configuration values updated:", Object.keys(changes));
    return changes;
  }

  notifyListeners(key, newValue, oldValue) {
    this.listeners.forEach((listener) => {
      try {
        listener(key, newValue, oldValue);
      } catch (error) {
        logger.error("Error in config listener:", error);
      }
    });
  }

  getAvailableProviders() {
    return [
      {
        id: "gemini",
        name: "Google Gemini",
        requiresKey: "GOOGLE_GENERATIVE_AI_API_KEY",
      },
      { id: "groq", name: "Groq", requiresKey: "GROQ_API_KEY" },
      { id: "openai", name: "OpenAI", requiresKey: "OPENAI_API_KEY" },
    ];
  }

  getAvailableModels(provider) {
    const models = {
      gemini: [
        "gemini-2.0-flash",
        "gemini-2.5-flash",
        "gemini-2.5-pro",
        "gemini-2.5-flash-lite-preview-06-17",
        "gemini-2.5-flash-preview-native-audio-dialog",
        "gemini-2.5-flash-preview-tts",
        "gemini-2.0-flash-preview-image-generation",
      ],
      groq: [
        "llama-3.3-70b-versatile",
        "llama-3.1-70b-versatile",
        "moonshotai/kimi-k2-instruct",
      ],
      openai: ["gpt-4o", "gpt-4o-mini"],
    };

    return models[provider] || [];
  }

  getAvailablePrompts() {
    return [
      {
        id: "turing",
        name: "Turing Test v1",
        description: "Original Turing test persona",
        defaultBotName: "Alex",
      },
      {
        id: "turing2",
        name: "Turing Test v2",
        description: "Enhanced Turing test persona",
        defaultBotName: "Sam",
      },
      {
        id: "turing3",
        name: "Turing Test v3",
        description: "Latest Turing test persona",
        defaultBotName: "Jordan",
      },
      {
        id: "turing4",
        name: "Turing Test v4",
        description: "Latest Turing test persona (default)",
        defaultBotName: "Peter",
      },
      {
        id: "tay",
        name: "Tay",
        description: "Tay persona",
        defaultBotName: "Tay",
      },
      {
        id: "persona",
        name: "Casual Persona",
        description: "Casual group chat member",
        defaultBotName: "Casey",
      },
      {
        id: "autism",
        name: "Academic",
        description: "Academic with dry humor",
        defaultBotName: "Dr. Morgan",
      },
      {
        id: "custom",
        name: "Custom Prompt",
        description: "Use your own custom prompt",
        defaultBotName: null, // Keep current bot name for custom prompts
      },
    ];
  }

  validateConfig(config) {
    const errors = [];

    if (
      !config.provider ||
      !["gemini", "groq", "openai"].includes(config.provider)
    ) {
      errors.push("Invalid provider. Must be 'gemini', 'groq', or 'openai'");
    }

    if (!config.model || typeof config.model !== "string") {
      errors.push("Model must be a non-empty string");
    }

    if (!config.botName || typeof config.botName !== "string") {
      errors.push("Bot name must be a non-empty string");
    }

    if (
      typeof config.rateLimit !== "number" ||
      config.rateLimit < 1 ||
      config.rateLimit > 100
    ) {
      errors.push("Rate limit must be a number between 1 and 100");
    }

    // Validate custom prompt when using custom template
    if (config.promptTemplate === "custom") {
      if (
        !config.customPrompt ||
        typeof config.customPrompt !== "string" ||
        config.customPrompt.trim().length === 0
      ) {
        errors.push("Custom prompt cannot be empty when using custom template");
      }
    }

    return errors;
  }
}

// Create singleton instance
const configService = new ConfigService();

module.exports = { ConfigService, configService };
